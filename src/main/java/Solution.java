

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import proxy.Dog;

import java.io.File;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.DelayQueue;
import java.util.stream.Collectors;


public class Solution {


    public int maxValue(int[][] grid) {
        int[][] maxValue = new int[grid.length][grid[0].length];
        maxValue[0][0] = grid[0][0];
        for (int i = 1; i < grid[0].length; i++) {
            maxValue[0][i] = maxValue[0][i - 1] + grid[0][i];
        }
        for (int i = 1; i < grid.length; i++) {
            maxValue[i][0] = maxValue[i - 1][0] + grid[i][0];
        }
        for (int i = 1; i < grid.length; i++) {
            for (int j = 1; j < grid[i].length; j++) {
                maxValue[i][j] = Math.max(maxValue[i - 1][j], maxValue[i][j - 1]) + grid[i][j];
            }
        }
        return maxValue[grid.length - 1][grid[0].length - 1];
    }

    private void deep(int x, int y, int[][] grid, int[][] maxValue, int value) {
        if (x > grid.length - 1 || y > grid[x].length - 1) {
            return;
        }
        value += grid[x][y];
        maxValue[x][y] = Math.max(maxValue[x][y], value);
        deep(x + 1, y, grid, maxValue, value);
        deep(x, y + 1, grid, maxValue, value);
    }

    public int minNumberOfHours(int initialEnergy, int initialExperience, int[] energy, int[] experience) {
        int en1 = initialEnergy;
        int ex1 = initialExperience;
        int total = 0;
        int cha;
        for (int i = 0; i < energy.length; i++) {
            if (energy[i] >= en1) {
                cha = energy[i] - en1 + 1;
                en1 += cha;
                total += cha;
            }
            if (experience[i] >= ex1) {
                cha = experience[i] - ex1 + 1;
                ex1 += cha;
                total += cha;
            }
            ex1 += experience[i];
            en1 -= energy[i];
        }
        return total;
    }

    public int maximalNetworkRank(int n, int[][] roads) {
        Set<Integer>[] sets = new Set[101];
        for (int[] road : roads) {
            if (sets[road[0]] == null) {
                sets[road[0]] = new HashSet<>();
            }
            if (sets[road[1]] == null) {
                sets[road[1]] = new HashSet<>();
            }
            sets[road[0]].add(road[1]);
            sets[road[1]].add(road[0]);
        }
        int max = 0;
        for (int i = 2; i < sets.length; i++) {
            for (int j = i + 1; j < sets.length; j++) {
                if (sets[i] != null && sets[j] != null) {
                    max = Math.max(max, sets[i].size() + sets[j].size() - (sets[i].contains(j) ? 1 : 0));
                }
            }
        }
        return max;
    }

    public int countSubarrays(int[] nums, int k) {
        int max = 0;
        for (int i = 0; i < nums.length; i++) {
            if (nums[i] == k) {
                int after = nums.length - 1 - i;

            }
        }
        return max;
    }

    public static void main(String[] args) {

        Solution solution = new Solution();
        int[][]a = {{0,1},{2,2},{0,3}};
        Map<String, Object> map = new HashMap<>();
        map.put("MA.a.b.c","1");
        map.put("MA.a.b.d","2");
        map.put("MA.a.c","1");

        System.out.println(solution.formatAdditionalData(map));
    }

    private static void test() throws ClassNotFoundException {
        String packageBase = Dog.class.getCanonicalName().replace(".Dog", "");
        String filePathBase = Dog.class.getResource("./").getFile();
        File dir = new File(filePathBase);
        System.out.println(filePathBase);
        List<Class> classes = new ArrayList<>();
        for (String fileName : dir.list()) {
            if (fileName.endsWith(".class")) {
                String name = fileName.replace(".class", "");
                classes.add(Class.forName(packageBase + "." + name));
            }
        }
    }


    public double[] convertTemperature(double celsius) {

        return new double[]{celsius + 273.15, celsius * 1.8 + 32};
    }

    public int[] evenOddBit(int n) {
        int even = 0, odd = 0;
        int count = 0;
        while (n != 0) {
            if ((n & 1) == 1) {
                if (count % 2 == 0) {
                    even++;
                } else {
                    odd++;
                }
            }
            count++;
            n >>= 1;
        }
        return new int[]{even, odd};
    }

    public List<Boolean> checkArithmeticSubarrays(int[] nums, int[] l, int[] r) {
        List<Boolean> result = new ArrayList<>();
        for (int i = 0; i < l.length; i++) {
            int array[] = new int[r[i] - l[i] + 1];
            for (int j = 0; j < array.length; j++) {
                array[j] = nums[l[i] + j];
            }
            Arrays.sort(array);
            result.add(isCha(array));
        }
        return result;
    }

    boolean isCha(int[] array) {
        if (array.length == 1) {
            return true;
        }
        int cha = array[1] - array[0];
        int finalNum = array[0] + cha * (array.length - 1);
        if (finalNum != array[array.length - 1]) {
            return false;
        }
        for (int i = 2; i < array.length; i++) {
            if (array[i] - array[i - 1] != cha) {
                return false;
            }
        }
        return true;
    }

    private String removeUseless(String pattern) {
        String right = " ";
        String template = "";
        int removeIndex = pattern.length();
        for (int i = pattern.length() - 1; i >= 0; i--) {
            char ch = pattern.charAt(i);
            if (ch == ' ') {
                if (!template.equals("")) {
                    if (template.equals("and") || template.equals("or")) {
                        if (right.equals(")") || right.equals(" ") || right.isEmpty()) {
                            removeIndex = i;
                        }
                    }
                    if (right.length() > 0 && Character.isDigit(right.charAt(0)) && right.equals("(")) {
                        removeIndex = i;
                    }
                    right = template;
                    template = "";
                }
                continue;
            }
            if (ch == '(') {
                if (right.equals(")") || right.equals("and") || right.equals("or")) {
                    removeIndex = i;
                }
            }
            if (ch == ')') {
                if (right.equals("(") || right.length() > 0 && Character.isDigit(right.charAt(0))) {
                    removeIndex = i;
                }
            }
            template = ch + template;
        }
        String result = pattern.substring(0, removeIndex);
        if (!pattern.equals(result)) {
            return removeUseless(result);
        }
        return result;
    }

    public int numDupDigitsAtMostN(int n) {
        return 0;
    }

    public int countSubstrings(String s, String t) {

        return 1;
    }

    public boolean primeSubOperation(int[] nums) {
        int maxNum = 0;
        for (int n : nums) {
            maxNum = Math.max(n, maxNum);
        }
        List<Integer> zhi = getZhi(maxNum);
        for (int i = nums.length - 2; i >= 0; i--) {

            if (nums[i] < nums[i + 1]) {
                continue;
            }
            for (Integer integer : zhi) {
                if (integer < nums[i] && nums[i] - integer < nums[i + 1]) {
                    nums[i] -= integer;
                    break;
                }
            }
            if (nums[i] >= nums[i + 1]) {
                return false;
            }
        }
        return true;
    }

    private List<Integer> getZhi(int num) {
        List<Integer> result = new ArrayList<>();
        for (int i = 2; i <= num; i++) {
            int half = (int) Math.sqrt(i) + 1;
            boolean flag = true;
            for (int j = 2; j < half; j++) {
                if (i % j == 0) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                result.add(i);
            }
        }
        return result;
    }

    public String maskPII(String s) {
        char[] array = s.toCharArray();
        if (Character.isAlphabetic(array[0])) {
            StringBuilder sb = new StringBuilder();
            sb.append(Character.toLowerCase(array[0]));
            sb.append("*****");
            boolean noAt = true;
            for (int i = 1; i < array.length; i++) {
                if (noAt) {
                    if (array[i + 1] == '@') {
                        sb.append(Character.toLowerCase(array[i]));
                        noAt = false;
                    }
                } else {
                    sb.append(Character.isAlphabetic(array[i]) ? Character.toLowerCase(array[i]) : array[i]);
                }
            }
            return sb.toString();

        } else {
            String tmp = "";
            for (int i = 0; i < s.length(); i++) {
                char c = s.charAt(i);
                if (Character.isDigit(c)) {
                    tmp += c;
                }
            }
            if (tmp.length() == 10) {
                return "***-***-" + tmp.substring(tmp.length() - 4);
            } else if (tmp.length() == 11) {
                return "+*-***-***-" + tmp.substring(tmp.length() - 4);
            } else if (tmp.length() == 12) {
                return "+**-***-***-" + tmp.substring(tmp.length() - 4);
            } else {
                return "+***-***-***-" + tmp.substring(tmp.length() - 4);
            }
        }
    }

    public void distributeSum() {
        int n = 100;
        int dp[][] = new int[n + 1][n + 1];
        int cost = 0;
        for (int i = n; i >= 1; i--) {
            for (int j = i + 1; j <= n; j++) {
                for (int k = i; k < j; k++) {
                    dp[i][j] = Math.max(dp[i][k] + dp[k + 1][j] + cost, dp[i][j]);
                }
            }
        }
    }


    public int longestDecomposition(String text) {
        int count = 0;
        int head = 0;
        for (int i = 0; i < text.length() / 2; i++) {
            if (fit(text, head, i)) {
                count++;
                head = i + 1;
            }
        }
        return count * 2 + (head * 2 < text.length() ? 1 : 0);
    }

    private boolean fit(String text, int start, int end) {
        int len = end - start + 1;
        for (int i = 0; i < len; i++) {
            if (text.charAt(start + i) != text.charAt(text.length() - end - 1 + i)) {
                return false;
            }
        }
        return true;
    }


    public int countDaysTogether(String arriveAlice, String leaveAlice, String arriveBob, String leaveBob) {
        if (leaveAlice.compareTo(arriveBob) < 0 || leaveBob.compareTo(arriveAlice) < 0) {
            return 0;
        }
        String arr[] = {arriveAlice, leaveAlice, arriveBob, leaveBob};
        Arrays.sort(arr);
        return count(arr[1], arr[2]);
    }

    private int count(String before, String after) {
        int days[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        String[] pieces1 = before.split("-");
        String[] pieces2 = after.split("-");
        int month1 = Integer.parseInt(pieces1[0]);
        int day1 = Integer.parseInt(pieces1[1]);
        int month2 = Integer.parseInt(pieces2[0]);
        int day2 = Integer.parseInt(pieces2[1]);
        int count = 0;
        if (month2 == month1) {
            count = day2 - day1 + 1;
        } else {
            count = day2 + days[month1 - 1] - day1 + 1;
            for (int i = month1 + 1; i < month2; i++) {
                count += days[i - 1];
            }
        }
        return count;
    }


    private String formCode(String pattern) {
        if (pattern == null) {
            return null;
        }
        String right = " ";
        String template = "";
        int count = 0;
        int removeIndex = pattern.length();
        for (int i = pattern.length() - 1; i >= 0; i--) {
            char ch = pattern.charAt(i);
            if (ch == ' ') {
                if (!template.equals("")) {
                    if (template.equals("and") || template.equals("or")) {
                        if (right.equals(")") || right.equals(" ") || right.isEmpty()) {
                            removeIndex = i;
                        }
                    }
                    if (right.length() > 0 && Character.isDigit(right.charAt(0)) && right.equals("(")) {
                        removeIndex = i;
                    }
                    right = template;
                    template = "";
                }
                continue;
            }
            if (ch == '(') {
                if (right.equals(")") || right.equals("and") || right.equals("or")) {
                    removeIndex = i;
                }
            }
            if (ch == ')') {
                if (right.equals("(") || right.length() > 0 && Character.isDigit(right.charAt(0))) {
                    removeIndex = i;
                }
            }
            template = ch + template;
        }
        String result = pattern.substring(0, removeIndex);
        if (!pattern.equals(result)) {
            return removeUseless(result);
        }
        return result.trim().length() == 0 ? null : result.trim();
    }


    public int maxValueAfterReverse(int[] nums) {

        int maxCount = 0;
        for (int i = 0; i < nums.length - 1; i++) {
            maxCount += Math.abs(nums[i] - nums[i + 1]);
        }


        return maxCount;
    }

    double sum = 0;

    public double frogPosition(int n, int[][] edges, int t, int target) {
        sum = 0;
        List<Integer>[] relations = new List[n + 1];
        int[] visited = new int[n + 1];
        for (int i = 0; i < relations.length; i++) {
            relations[i] = new ArrayList<>();
        }

        for (int[] edge : edges) {
            relations[edge[0]].add(edge[1]);
            relations[edge[1]].add(edge[0]);
        }
        visited[1] = 1;
        deepJump(1, 0, target, t, 1.0, relations, visited);
        return sum;
    }

    void deepJump(int position, int step, int target, int maxStep, double baseScore, List<Integer>[] relation, int[] visited) {
        if (position == target) {
            if (step == maxStep) {
                sum += baseScore;
            } else if (step < maxStep) {
                List<Integer> bros = relation[position];
                if (bros.isEmpty() || bros.stream().allMatch(a -> visited[a] == 1)) {
                    sum += baseScore;
                }
            }
            return;
        }
        if (step > maxStep) {
            return;
        }
        List<Integer> bros = new ArrayList<>();
        for (Integer p : relation[position]) {
            if (visited[p] == 0) {
                bros.add(p);
            }
        }
        double newScore = baseScore / bros.size();
        for (int bro : bros) {
            visited[bro] = 1;
            deepJump(bro, step + 1, target, maxStep, newScore, relation, visited);
            visited[bro] = 0;
        }
    }


    public int minLength(String s) {
        Stack<Character> stack = new Stack<>();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (!stack.isEmpty() && (c == 'B' && stack.peek() == 'A' || c == 'D' && stack.peek() == 'C')) {
                stack.pop();
            } else {
                stack.add(c);
            }
        }
        return stack.size();
    }

    public String makeSmallestPalindrome(String s) {
        char[] chars = s.toCharArray();
        int loopSize = s.length() / 2;
        for (int i = 0; i < loopSize; i++) {
            int right = s.length() - 1 - i;
            if (chars[i] < chars[right]) {
                chars[right] = chars[i];
            } else if (chars[i] > chars[right]) {
                chars[i] = chars[right];
            }
        }
        return new String(chars);
    }

    public int punishmentNumber(int n) {
        int result = 0;
        for (int i = 1; i <= n; i++) {
            int sqrt = i * i;
            if (chengfa(i, String.valueOf(sqrt), 0, 0)) {
                System.out.println(i);
                result += sqrt;
            }
        }
        return result;
    }

    public boolean chengfa(int base, String sqrt, int start, int sum) {
        if (base == sum && start == sqrt.length()) {
            return true;
        }
        if (sum > base) {
            return false;
        }
        for (int len = 1; len <= sqrt.length() - start; len++) {
            int tmp = sum;
            for (int i = start; i < sqrt.length(); i += len) {
                tmp += Integer.valueOf(sqrt.substring(i, Math.min(i + len, sqrt.length())));
                if (chengfa(base, sqrt, i + len, tmp)) {
                    return true;
                }
            }
        }
        return false;
    }


    public int maxRepOpt1(String text) {
        text = "A" + text + "B";
        Map<Character, Integer> leftCharMap = new HashMap<>();
        Map<Character, Integer> rightCharMap = new HashMap<>();
        //以i结尾最长相同子串
        int[] leftMaxLen = new int[text.length()];
        int[] rightMaxLen = new int[text.length()];
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            leftCharMap.putIfAbsent(c, i);
        }
        for (int i = text.length() - 1; i >= 0; i--) {
            rightCharMap.putIfAbsent(text.charAt(i), i);
        }
        leftMaxLen[0] = 1;
        rightMaxLen[text.length() - 1] = 1;
        for (int i = 1; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == text.charAt(i - 1)) {
                leftMaxLen[i] = leftMaxLen[i - 1] + 1;
            } else {
                leftMaxLen[i] = 1;
            }
        }
        for (int i = text.length() - 2; i >= 0; i--) {
            char c = text.charAt(i);
            if (c == text.charAt(i + 1)) {
                rightMaxLen[i] = rightMaxLen[i + 1] + 1;
            } else {
                rightMaxLen[i] = 1;
            }
        }
        int max = 0;

        for (int i = 1; i < text.length() - 1; i++) {
            char left = text.charAt(i - 1);
            char right = text.charAt(i + 1);
            char mid = text.charAt(i);
            if (mid != left && left == right) {
                int tmp = leftMaxLen[i - 1] + rightMaxLen[i + 1] + 1;
                if (leftCharMap.get(left) >= i - leftMaxLen[i - 1] && rightCharMap.get(right) <= i + rightMaxLen[i + 1]) {
                    tmp -= 1;
                }
                max = Math.max(max, tmp);
            }
            if (leftCharMap.get(left) > i - leftMaxLen[i - 1] || rightCharMap.get(left) > i) {
                max = Math.max(max, leftMaxLen[i - 1] + 1);
            }
            if (rightCharMap.get(right) > i + rightMaxLen[i + 1] || leftCharMap.get(right) < i) {
                max = Math.max(max, rightMaxLen[i + 1] + 1);
            }

            max = Math.max(max, Math.max(leftMaxLen[i], rightMaxLen[i]));
        }
        return max;
    }

    public int minimizedStringLength(String s) {
        int count = 0;
        int[] map = new int[26];
        for (int i = 0; i < s.length(); i++) {
            int index = s.charAt(i) - 'a';
            map[index]++;
            if (map[index] == 1) {
                count++;
            }
        }
        return count;
    }

    public int semiOrderedPermutation(int[] nums) {
        int small = 0;
        int big = 0;
        for (int i = 0; i < nums.length; i++) {
            if (nums[i] == 1) {
                small = i;
            } else if (nums[i] == nums.length) {
                big = i;
            }
        }
        int result = small;

        if (big != nums.length) {
            result += nums.length - big - 1;
        }

        return small < big ? result : result - 1;
    }

    public long matrixSumQueries(int n, int[][] queries) {
        int xCount = n;
        int yCount = n;
        boolean[] x = new boolean[n];
        boolean[] y = new boolean[n];
        long result = 0;
        for (int i = queries.length - 1; i >= 0 && xCount != 0 && yCount != 0; i--) {
            int[] query = queries[i];
            if (query[0] == 0 && !x[query[1]]) {
                result += yCount * query[2];
                x[query[1]] = true;
                xCount--;
            }
            if (query[0] == 1 && !y[query[1]]) {
                result += xCount * query[2];
                y[query[1]] = true;
                yCount--;
            }
        }
        return result;
    }


    public int unequalTriplets(int[] nums) {
        //index[i] 到i为止小于nums[i]的数量
        int[] index = new int[nums.length];
        int count = 0;
        for (int i = 1; i < nums.length; i++) {
            if (nums[i - 1] != nums[i]) {
                index[i] = index[i - 1] + 1;
            } else if (nums[i - 1] == nums[i]) {
                index[i] = index[i - 1];
            }
        }
        for (int i = 2; i < nums.length; i++) {
            for (int j = i - 1; j >= 0; j--) {
                if (nums[j] != nums[i]) {
                    count += index[j];
                    break;
                }
            }
        }
        return count;
    }

    public int minNumberOfSemesters(int n, int[][] relations, int k) {
        Map<Integer, Node> nodeMap = new HashMap<>();
        for (int[] relation : relations) {
            Node pre = nodeMap.getOrDefault(relation[0], new Node(relation[0]));
            Node after = nodeMap.getOrDefault(relation[1], new Node(relation[1]));
            pre.setNode(after);
            nodeMap.putIfAbsent(pre.num, pre);
            nodeMap.putIfAbsent(after.num, after);
        }
        for (int i = 1; i <= n; i++) {
            nodeMap.putIfAbsent(i, new Node(i));
        }


        int terms = 0;
        Queue<Node> queue = new PriorityQueue<>((a, b) -> a.count - b.count);

        queue.addAll(nodeMap.values());
        while (!queue.isEmpty()) {
            List<Node> nodes = new ArrayList<>();
            Queue<Node> queue2 = new PriorityQueue<>((a, b) -> a.count - b.count);
            while (nodes.size() < k && !queue.isEmpty() && queue.peek().count == 0) {
                nodes.add(queue.poll());
            }

            terms++;
            for (Node node : nodes) {
                for (int i = 0; i < node.nextNodes.length; i++) {
                    if (node.nextNodes[i] != null) {
                        node.removeNode(node.nextNodes[i].num);
                    }
                }
            }
            while (!queue.isEmpty()) {
                queue2.add(queue.poll());
            }
            queue = queue2;
        }
        return terms;
    }

    class Node {
        int num;

        int count;
        Node[] nextNodes;

        Node[] preNodes;

        public Node(int num) {
            this.num = num;
            nextNodes = new Node[16];
            preNodes = new Node[16];
            count = 0;
        }

        public void setNode(Node node) {
            if (nextNodes[node.num] != null) {
                return;
            }
            nextNodes[node.num] = node;
            node.preNodes[num] = this;
            node.count++;
        }

        public Node removeNode(int index) {
            if (nextNodes[index] == null) {
                return null;
            }
            Node node = nextNodes[index];
            node.count--;
            nextNodes[index] = null;
            node.preNodes[num] = null;
            return node;
        }
    }


    public boolean checkOverlap(int radius, int xCenter, int yCenter, int x1, int y1, int x2, int y2) {


        return false;
    }

    public int countBeautifulPairs(int[] nums) {
        int count = 0;
        for (int i = 0; i < nums.length; i++) {
            for (int j = i + 1; j < nums.length; j++) {
                int first = getFirst(nums[i]);
                int last = nums[j] % 10;
                if (gcd(first, last) == 1) {
                    count++;
                }
            }
        }
        return count;
    }

    private int getFirst(int num) {
        while (num >= 10) {
            num /= 10;
        }
        return num;
    }

    private int gcd(int a, int b) {
        if (b == 0) {
            return a;
        }
        return gcd(b, a % b);
    }

    public int makeTheIntegerZero(int num1, int num2) {
        if (num2 >= num1) {
            return -1;
        }
        return 1;
    }

    public int numberOfGoodSubarraySplits(int[] nums) {
        List<Integer> params = new ArrayList<>();
        int MOD = 1000000007;
        long result = 0;
        int count = 0;
        for (int i = 0; i < nums.length; i++) {
            count++;
            if (nums[i] == 1) {
                params.add(count);
                count = 0;
            }
        }
        if (params.size() > 0) {
            result = 1;
            for (int i = 1; i < params.size(); i++) {
                result = (result * params.get(i)) % MOD;
            }
        }
        return (int) result;
    }

    public int longestAlternatingSubarray(int[] nums, int threshold) {
        if (nums.length == 1) {
            if (nums[0] <= threshold && nums[0] % 2 == 0) {
                return 1;
            } else {
                return 0;
            }
        }
        int start = 0;
        int max = 0;
        while (start < nums.length && (nums[start] > threshold || nums[start] % 2 != 0)) {
            start++;
        }
        for (int i = start + 1; i < nums.length; i++) {
            if (nums[i] > threshold) {
                max = Math.max(max, i - start);
                while (++i < nums.length && (nums[i] > threshold || nums[i] % 2 != 0)) ;
                start = i;
                continue;
            }
            if (nums[i] % 2 == nums[i - 1] % 2) {
                max = Math.max(max, i - start);
                while (i < nums.length && (nums[i] > threshold || nums[i] % 2 != 0)) {
                    i++;
                }
                start = i;
            }
        }
        if (start < nums.length) {
            max = Math.max(max, nums.length - start);
        }
        return max;
    }


    public List<List<Integer>> findPrimePairs(int n) {
        List<List<Integer>> result = new ArrayList<>();

        for (int i = 2; i * 2 <= n; i++) {
            if (zhi(i)) {
                int p2 = n - i;
                if (zhi(p2)) {
                    List<Integer> pair = new ArrayList<>();
                    pair.add(i);
                    pair.add(p2);
                    result.add(pair);
                }
            }
        }
        return result;
    }

    private boolean zhi(int num) {
        for (int i = 2; i * i <= num; i++) {
            if (num % i == 0) {
                return false;
            }
        }
        return true;
    }

    public int minFallingPathSum(int[][] matrix) {
        int[][] result = new int[2][matrix[0].length];
        for (int i = 0; i < matrix.length; i++) {
            int pre = (i + 1) % 2;
            int current = i % 2;
            for (int j = 0; j < matrix[i].length; j++) {
                if (i == 0) {
                    result[current][j] = matrix[i][j];
                } else {
                    int min = result[pre][j];
                    if (j > 0) {
                        min = Math.min(min, result[pre][j - 1]);
                    }
                    if (j < matrix[i].length - 1) {
                        min = Math.min(min, result[pre][j + 1]);
                    }
                    result[current][j] = min + matrix[i][j];
                }
            }
        }
        int index = (matrix.length + 1) % 2;
        int min = result[index][0];
        for (int i = 1; i < result[index].length; i++) {
            min = Math.min(result[index][i], min);
        }
        return min;
    }


    public int maximumJumps(int[] nums, int target) {
        //step[i] 表示到达i所需要花费的最大步数
        int[] steps = new int[nums.length];
        Arrays.fill(steps, -1);
        steps[0] = 0;
        for (int i = 1; i < nums.length; i++) {
            for (int j = i - 1; j >= 0; j--) {
                if (steps[j] != -1 && Math.abs(nums[i] - nums[j]) <= target) {
                    steps[i] = Math.max(steps[i], steps[j] + 1);
                }
            }
        }
        return steps[nums.length - 1];
    }


    public int maxNonDecreasingLength(int[] nums1, int[] nums2) {
        //dp1[i]表示以nums1[i]结尾的非递减子数组长度
        int dp1[] = new int[nums1.length];
        //dp2[i]表示以nums2[i]结尾的非递减子数组长度
        int dp2[] = new int[nums2.length];
        Arrays.fill(dp1, 1);
        Arrays.fill(dp2, 1);
        for (int i = 1; i < nums1.length; i++) {
            if (nums1[i] >= nums1[i - 1]) {
                dp1[i] = Math.max(dp1[i - 1] + 1, dp1[i]);
            }
            if (nums1[i] >= nums2[i - 1]) {
                dp1[i] = Math.max(dp2[i - 1] + 1, dp1[i]);
            }

            if (nums2[i] >= nums1[i - 1]) {
                dp2[i] = Math.max(dp1[i - 1] + 1, dp2[i]);
            }
            if (nums2[i] >= nums2[i - 1]) {
                dp2[i] = Math.max(dp2[i - 1] + 1, dp2[i]);
            }
        }
        int max = 0;
        for (int i = 0; i < dp1.length; i++) {
            max = Math.max(Math.max(dp1[i], dp2[i]), max);
        }
        return max;
    }

    public boolean checkArray(int[] nums, int k) {
        if (k == 1) {
            return true;
        }
        int cha[] = new int[nums.length + 1];
        cha[0] = nums[0];
        for (int i = 1; i < nums.length; i++) {
            cha[i] = nums[i] - nums[i - 1];
        }
        for (int i = 0; i + k <= nums.length; i++) {
            if (cha[i] > 0) {
                cha[i + k] += cha[i];
                cha[i] = 0;
            } else if (cha[i] < 0) {
                return false;
            }
        }
        for (int i = 0; i < nums.length; i++) {
            if (cha[i] != 0) {
                return false;
            }
        }
        return true;
    }

    public String addStrings(String num1, String num2) {
        char[] pre = reverse(num1);
        char[] after = reverse(num2);
        int carry = 0;
        int index1 = 0;
        int index2 = 0;
        StringBuilder result = new StringBuilder();
        while (index1 < pre.length && index2 < after.length) {
            int num = pre[index1] + after[index2] + carry - 2 * '0';
            result.append((char) (num % 10 + '0'));
            carry = num / 10;
            index1++;
            index2++;
        }
        while (index1 < pre.length) {
            int num = pre[index1] + carry - '0';
            result.append((char) (num % 10 + '0'));
            carry = num / 10;
            index1++;
        }
        while (index2 < after.length) {
            int num = after[index2] + carry - '0';
            result.append((char) (num % 10 + '0'));
            carry = num / 10;
            index2++;
        }
        if (carry != 0) {
            result.append((char) (carry + '0'));
        }
        return new String(reverse(result.toString()));
    }

    private char[] reverse(String s) {
        char[] re = new char[s.length()];
        for (int i = 0; i < s.length(); i++) {
            re[re.length - i - 1] = s.charAt(i);
        }
        return re;
    }


    public class TreeNode {
        int val;
        TreeNode left;
        TreeNode right;

        TreeNode() {
        }

        TreeNode(int val) {
            this.val = val;
        }

        TreeNode(int val, TreeNode left, TreeNode right) {
            this.val = val;
            this.left = left;
            this.right = right;
        }
    }

    Map<Integer, Integer> minNumberInLayer = new HashMap<>();

    int max = 1;

    public int widthOfBinaryTree(TreeNode root) {
        dfs(root, 0, 0);
        return max;
    }

    private void dfs(TreeNode node, int layer, int number) {
        if (node == null) {
            return;
        }
        minNumberInLayer.putIfAbsent(layer, number);
        int small = minNumberInLayer.get(layer);
        max = Math.max(max, number - small + 1);
        dfs(node.left, layer + 1, 2 * number + 1);
        dfs(node.right, layer + 1, 2 * number + 2);
    }

    public int robotSim(int[] commands, int[][] obstacles) {
        int x = 0;
        int y = 0;
        int dy[] = {1, 0, -1, 0};
        int dx[] = {0, 1, 0, -1};
        int direction = 0;
        int max = 0;
        Set<Integer> set = new HashSet<>();
        for (int[] obstacle : obstacles) {
            set.add(obstacle[0] * 60001 + obstacle[1]);
        }
        for (int command : commands) {
            if (command == -1) {
                direction = (direction + 1) % 4;
            } else if (command == -2) {
                direction = (direction + 3) % 4;
            } else {
                for (int i = 0; i < command; i++) {
                    int nextX = x + dx[direction];
                    int nextY = y + dy[direction];
                    if (set.contains(nextX * 60001 + nextY)) {
                        break;
                    }
                    x = nextX;
                    y = nextY;
                }
                max = Math.max(max, x * x + y * y);
            }
        }
        return max;
    }

    public int findMaxValueOfEquation(int[][] points, int k) {
        //y - x
        int max = Integer.MIN_VALUE;
        PriorityQueue<int[]> queue = new PriorityQueue<>((a, b) -> b[0] - a[0]);
        for (int[] point : points) {
            while (!queue.isEmpty() && point[0] - queue.peek()[1] > k) {
                queue.poll();
            }
            if (!queue.isEmpty()) {
                max = Math.max(max, queue.peek()[0] + point[1] + point[0]);
            }
            queue.add(new int[]{point[1] - point[0], point[0]});
        }
        return max;
    }

    public int halveArray(int[] nums) {
        double sum = 0;
        PriorityQueue<Double> q = new PriorityQueue<>((a, b) -> b - a >= 0 ? 1 : -1);
        for (int num : nums) {
            sum += num;
            q.add((double) num);
        }
        sum /= 2;
        int count = 0;
        while (sum > 0) {
            double biggest = q.poll();
            biggest /= 2;
            sum -= biggest;
            count++;
            q.add(biggest);
        }
        return count;
    }

    public List<String> splitWordsBySeparator(List<String> words, char separator) {
        List<String> result = new ArrayList<>();
        for (String word : words) {
            int start = 0;
            for (int i = 0; i < word.length(); i++) {
                char c = word.charAt(i);
                if (c == separator) {
                    if (i > start) {
                        result.add(word.substring(start, i));
                    }
                    start = i + 1;
                }
            }
            if (start < word.length()) {
                result.add(word.substring(start));
            }
        }
        return result;
    }

    public long maxArrayValue1(int[] nums) {
        Stack<Long> st = new Stack<>();
        st.add((long) nums[0]);
        long sum = 0;
        for (int i = 1; i < nums.length; i++) {
            if (nums[i] >= nums[i - 1]) {
                sum += nums[i];
            } else {
                while (!st.isEmpty() && sum >= st.peek()) {
                    sum += st.pop();
                }
                st.add(sum);
                sum = nums[i];
            }
        }
        if (!st.isEmpty() && sum != st.peek()) {
            while (!st.isEmpty() && sum >= st.peek()) {
                sum += st.pop();
            }
            st.add(sum);
        }

        long max = 0;
        for (Long num : st) {
            max = Math.max(num, max);
        }
        return max;
    }


    public long maxArrayValue(int[] nums) {
        long max = 0;
        if (nums.length == 1) {
            return nums[0];
        }
        long sum = nums[nums.length - 1];
        for (int i = nums.length - 2; i >= 0; i--) {
            if (nums[i] <= sum) {
                sum += nums[i];
            } else {
                max = Math.max(max, sum);
                sum = nums[i];
            }
        }
        max = Math.max(max, sum);
        return max;
    }

    public List<String> removeComments(String[] source) {
        List<String> result = new ArrayList<>();
        int block = 0;
        StringBuilder sb = new StringBuilder();
        for (String code : source) {
            for (int i = 0; i < code.length(); i++) {
                char nowChar = code.charAt(i);
                //块注释
                if (block == 1) {
                    if (i != code.length() - 1 && code.charAt(i + 1) == '/' && nowChar == '*') {
                        block = 0;
                        i++;
                    }
                } else if (block == 2) {
                    continue;
                } else {
                    if (i != code.length() - 1) {
                        if (code.charAt(i + 1) == '*' && nowChar == '/') {
                            block = 1;
                            i++;
                            continue;
                        } else if (code.charAt(i + 1) == '/' && nowChar == '/') {
                            block = 2;
                            i++;
                            continue;
                        }
                    }
                    sb.append(nowChar);
                }
            }
            if (block == 2) {
                block = 0;
                if (sb.length() > 0) {
                    result.add(sb.toString());
                    sb = new StringBuilder();
                }
            } else if (block == 0) {
                if (sb.length() > 0) {
                    result.add(sb.toString());
                    sb = new StringBuilder();
                }
            }
        }
        return result;
    }

    public int countCompleteSubarrays(int[] nums) {

        Set<Integer> set = new HashSet<>();
        for (int num : nums) {
            set.add(num);
        }
        int maxNumberCount = set.size();
        int left = 0;
        int right = 0;
        set.clear();
        for (int i = 0; i < nums.length; i++) {
            set.add(nums[i]);
            if (set.size() == maxNumberCount) {
                right = i;
                break;
            }
        }
        set.clear();
        for (int i = right; i >= 0; i--) {
            set.add(nums[i]);
            if (set.size() == maxNumberCount) {
                left = i;
                break;
            }
        }
        return (left + 1) * (nums.length - right);
    }

    public void reverseString(char[] s) {
        char c;
        for (int i = 0; i < s.length / 2; i++) {
            c = s[i];
            s[i] = s[s.length - 1 - i];
            s[s.length - 1 - i] = c;
        }
    }


    public String findReplaceString(String s, int[] indices, String[] sources, String[] targets) {
        StringBuilder result = new StringBuilder();
        List<Integer> indexes = new ArrayList<>();
        for (int i = 0; i < indices.length; i++) {
            indexes.add(i);
        }
        indexes.sort(Comparator.comparingInt(a -> indices[a]));

        int k = 0;
        for (int i = 0; i < s.length(); ) {
            int index;
            if (k < indexes.size() && i == indices[index = indexes.get(k)]) {
                int j;
                for (j = i; j < s.length() && j < i + sources[index].length(); j++) {
                    if (s.charAt(j) != sources[index].charAt(j - i)) {
                        break;
                    }
                }
                if (j == i + sources[index].length()) {
                    result.append(targets[index]);
                    i = j;
                } else {
                    result.append(s.charAt(i));
                    i++;
                }
                k++;
            } else {
                result.append(s.charAt(i));
                i++;
            }
        }
        return result.toString();
    }

    public int[] circularGameLosers(int n, int k) {
        int[] sign = new int[n];
        int start = 0;
        int step = k;
        while (sign[start] == 0) {
            sign[start] = 1;
            start = (start + step) % n;
            step += k;
        }
        List<Integer> result = new ArrayList<>();
        for (int i = 0; i < sign.length; i++) {
            if (sign[i] == 0) {
                result.add(i + 1);
            }
        }
        int[] r = new int[result.size()];
        for (int i = 0; i < result.size(); i++) {
            r[i] = result.get(i);
        }
        return r;
    }


    public int maxSum(int[] nums) {
        int max = -1;
        for (int i = 0; i < nums.length; i++) {
            for (int j = i + 1; j < nums.length; j++) {
                if (findMax(nums[i]) == findMax(nums[j])) {
                    max = Math.max(max, nums[i] + nums[j]);
                }
            }
        }
        return max;
    }

    private int findMax(int a) {
        int max = 0;
        while (a > 0) {
            max = Math.max(max, a % 10);
            a /= 10;
        }
        return max;
    }


    public class ListNode {
        int val;
        ListNode next;

        ListNode() {
        }

        ListNode(int val) {
            this.val = val;
        }

        ListNode(int val, ListNode next) {
            this.val = val;
            this.next = next;
        }
    }

    public ListNode doubleIt(ListNode head) {
        List<Integer> array = new ArrayList<>();
        while (head != null) {
            array.add(head.val);
            head = head.next;
        }
        ListNode emptyHead = new ListNode(-1);
        int c = 0;
        for (int i = array.size() - 1; i >= 0; i--) {
            int num = array.get(i) * 2 + c;
            ListNode newOne = new ListNode(num % 10, emptyHead.next);
            emptyHead.next = newOne;
            c = num / 10;
        }
        if (c != 0) {
            ListNode newOne = new ListNode(c, emptyHead.next);
            emptyHead.next = newOne;
        }
        return emptyHead.next;
    }

    public int minAbsoluteDifference1(List<Integer> nums, int x) {
        int min = Integer.MAX_VALUE;
        PriorityQueue<Integer> minQueue = new PriorityQueue<>((a, b) -> b - a);
        PriorityQueue<Integer> maxQueue = new PriorityQueue<>((a, b) -> b - a);
        for (int i = nums.size() - 1; i >= x; i--) {
            maxQueue.add(nums.get(i));
        }

        for (int i = 0; i < nums.size() - x; i++) {
            minQueue.add(nums.get(i));
            min = Math.min(min, Math.abs(maxQueue.peek() - minQueue.peek()));
            maxQueue.remove(nums.get(i + x));
        }
        return min;
    }

    public int minAbsoluteDifference(List<Integer> nums, int x) {
        int min = Integer.MAX_VALUE;
        TreeSet<Integer> treeSet = new TreeSet<>();
        Map<Integer, Integer> count = new HashMap<>();
        for (int i = nums.size() - 1; i >= x; i--) {
            treeSet.add(nums.get(i));
            count.put(nums.get(i), count.getOrDefault(nums.get(i), 0) + 1);
        }

        for (int i = 0; i < nums.size() - x; i++) {
            int aim = nums.get(i);
            Integer least = treeSet.ceiling(aim);
            if (least != null) {
                min = Math.min(min, Math.abs(aim - least));
            }
            least = treeSet.floor(aim);
            if (least != null) {
                min = Math.min(min, Math.abs(aim - least));
            }
            count.put(nums.get(i + x), count.get(nums.get(i + x)) - 1);
            if (count.get(nums.get(i + x)) == 0) {
                treeSet.remove(nums.get(i + x));
            }
        }
        return min;
    }


    public int maxSizeSlices(int[] slices) {
        List<Integer> list = new LinkedList<>();
        for (int i = 0; i < slices.length; i++) {
            list.add(slices[i]);
        }
        int sum = 0;
        while (!list.isEmpty()) {
            int index = 0;
            for (int i = 1; i < list.size(); i++) {
                if (list.get(index) < list.get(i)) {
                    index = i;
                }
            }
            sum += list.get(index);
            for (int i = 0; i < 3; i++) {
                list.remove((index - 1 + list.size()) % list.size());
            }
        }
        return sum;
    }


    public boolean canChange(String start, String target) {

        int last = -1;
        int targetIndex = 0;
        for (int i = 0; i < start.length(); i++) {
            char c = start.charAt(i);
            if (c == '_') {
                continue;
            } else {
                while (targetIndex < target.length() && target.charAt(targetIndex) == '_') {
                    targetIndex++;
                }
                if (targetIndex >= target.length()) {
                    return false;
                }
                if (c == 'L') {
                    if (i < targetIndex || c != target.charAt(targetIndex) || targetIndex <= last) {
                        return false;
                    }
                    last = targetIndex;
                    targetIndex++;
                }
                if (c == 'R') {
                    if (i > targetIndex || c != target.charAt(targetIndex)) {
                        return false;
                    }
                    while (i + 1 < start.length() && start.charAt(i + 1) == '_') {
                        i++;
                    }
                    if (i < targetIndex) {
                        return false;
                    }
                    last = targetIndex;
                    targetIndex++;
                }
            }
        }
        while (targetIndex < target.length() && target.charAt(targetIndex) == '_') {
            targetIndex++;
        }
        return targetIndex == target.length();
    }


    public int maxDistToClosest(int[] seats) {
        int left = -1;
        int max = 0;
        for (int i = 0; i < seats.length; i++) {
            if (seats[i] == 1) {
                if (left == -1) {
                    max = i;
                } else {
                    max = Math.max(max, (i - left) / 2);
                }
                left = i;
            }
        }
        if (left != seats.length - 1) {
            max = Math.max(max, seats.length - 1 - left);
        }
        return max;
    }

    public int[] countPairs(int n, int[][] edges, int[] queries) {
        int[] answer = new int[queries.length];
        int count[] = new int[n + 1];
        Map<Long, Integer> coupleMap = new HashMap<>();
        for (int[] arr : edges) {
            count[arr[0]]++;
            count[arr[1]]++;
            Long key = getKey(arr[0], arr[1]);
            coupleMap.put(key, coupleMap.getOrDefault(key, 0) + 1);
        }

        for (int after = 2; after <= n; after++) {
            for (int before = 1; before < after; before++) {
                int dump = coupleMap.getOrDefault(getKey(before, after), 0);
                int sum = count[before] + count[after] - (Math.max(dump, 0));
                for (int i = 0; i < queries.length; i++) {
                    int limit = queries[i];
                    if (sum > limit) {
                        answer[i]++;
                    }
                }
            }
        }

        return answer;
    }

    private Long getKey(int a, int b) {
        Long key;
        if (a < b) {
            key = a * 100000L + b;
        } else {
            key = b * 100000L + a;
        }
        return key;
    }


    public int goodNodes(TreeNode root) {
        return goodNodes(root, Integer.MIN_VALUE);
    }

    private int goodNodes(TreeNode node, int lastMax) {
        if (node == null) {
            return 0;
        }
        int base;
        if (lastMax <= node.val) {
            base = 1;
        } else {
            base = 0;
        }
        int maxNow = Math.max(node.val, lastMax);
        base += goodNodes(node.left, maxNow) + goodNodes(node.right, maxNow);
        return base;
    }

    public long waysToBuyPensPencils(int total, int cost1, int cost2) {
        long count = 0;
        int maxCount1 = total / cost1;
        for (long i = 0; i <= maxCount1; i++) {
            count += (total - cost1 * i) / cost2 + 1;
        }
        return count;
    }


    //max(node.left)== max(node.right) && node.deep = max
    TreeNode maxNode;
    int maxDeep;

    int fatherMaxDeep;

    public TreeNode lcaDeepestLeaves(TreeNode root) {
        maxDeep = 0;
        fatherMaxDeep = 9999999;
        maxNode = root;
        calculateDeep(root, 0);
        return maxNode;
    }

    public int calculateDeep(TreeNode node, int deep) {
        if (node == null) {
            return deep - 1;
        }
        int leftDeep = calculateDeep(node.left, deep + 1);
        int rightDeep = calculateDeep(node.right, deep + 1);
        int max = Math.max(leftDeep, rightDeep);
        if (max > maxDeep) {
            maxDeep = max;
            fatherMaxDeep = max;
        }
        if (leftDeep == rightDeep && max == maxDeep && fatherMaxDeep >= max) {
            fatherMaxDeep = max;
            maxNode = node;
        }
        return max;
    }


    public long repairCars(int[] ranks, int cars) {
        long time = 1;
        long maxTime = Long.MAX_VALUE;
        //（time,maxTime】内存在最小值  因为是取右边 累加的左边 那么不需要多判断一次  如果去左边 左边又没有增量 因为int取整导致 所以得+1判断
        while (time < maxTime) {
            long mid = (time + maxTime) / 2;
            if (canRepair(ranks, cars, mid)) {
                maxTime = mid;
            } else {
                time = mid + 1;
            }
        }
        return maxTime;
    }

    public boolean canRepair(int[] ranks, int cars, long time) {
        for (int i = 0; i < ranks.length && cars > 0; i++) {
            cars -= (long) Math.sqrt(time / ranks[i]);
        }
        return cars <= 0;
    }

    public int countSymmetricIntegers(int low, int high) {
        int count = 0;
        for (int i = low; i <= high; i++) {
            if (isSymmetric(i)) {
                count++;
            }
        }
        return count;
    }

    private boolean isSymmetric(int num) {
        if (num < 10) {
            return false;
        } else if (num < 100) {
            return num % 10 == num / 10;
        } else if (num < 1000) {
            return false;
        } else {
            int pre = num / 100;
            int after = num % 100;
            return pre / 10 + pre % 10 == after / 10 + after % 10;
        }
    }

    public int minimumOperations(String num) {
        int min = num.length();
        min = Math.min(min, findAB(num, '0', '0'));
        min = Math.min(min, findAB(num, '2', '5'));
        min = Math.min(min, findAB(num, '5', '0'));
        min = Math.min(min, findAB(num, '7', '5'));
        int nonZero = 0;
        for (int i = 0; i < num.length(); i++) {
            if (num.charAt(i) != '0') {
                nonZero++;
            }
        }
        min = Math.min(min, nonZero);
        return min;
    }

    private int findAB(String num, char a, char b) {
        int left = -1;
        int right = -1;
        int i;
        for (i = num.length() - 1; i >= 0; i--) {
            if (num.charAt(i) == b) {
                right = i;
                i--;
                break;
            }
        }
        if (right == -1) {
            return 99999;
        }
        for (; i >= 0; i--) {
            if (num.charAt(i) == a) {
                left = i;
                break;
            }
        }
        if (left == -1) {
            return 99999;
        }
        return right - left - 1 + num.length() - right - 1;
    }


    public long countInterestingSubarrays(List<Integer> nums, int modulo, int k) {
        //dp[i]表示 【0，i]符合条件的数量
        int[] dp = new int[nums.size()];
        dp[0] = nums.get(0) % modulo == k ? 1 : 0;
        for (int i = 1; i < nums.size(); i++) {
            if (nums.get(i) % modulo == k) {
                dp[i] = dp[i - 1] + 1;
            } else {
                dp[i] = dp[i - 1];
            }
        }
        long result = 0;

        return result;
    }

    public int giveGem(int[] gem, int[][] operations) {
        for (int i = 0; i < operations.length; i++) {
            gem[operations[i][1]] += (gem[operations[i][0]] / 2);
        }
        int max = Integer.MIN_VALUE;
        int min = Integer.MAX_VALUE;
        for (int i = 0; i < gem.length; i++) {
            max = Math.max(max, gem[i]);
            min = Math.min(min, gem[i]);
        }
        return max - min;
    }

    Map<TreeNode, Integer> getMap = new HashMap<>();
    Map<TreeNode, Integer> noGetMap = new HashMap<>();

    public int rob(TreeNode root) {
        calculateTree(root);
        return Math.max(getMap.getOrDefault(root, 0), noGetMap.getOrDefault(root, 0));
    }

    private void calculateTree(TreeNode root) {
        if (root == null) {
            return;
        }
        if (getMap.get(root.left) == null) {
            calculateTree(root.left);
        }
        if (getMap.get(root.right) == null) {
            calculateTree(root.right);
        }
        getMap.put(root, root.val + noGetMap.getOrDefault(root.left, 0) + noGetMap.getOrDefault(root.right, 0));
        noGetMap.put(root, Math.max(getMap.getOrDefault(root.left, 0), noGetMap.getOrDefault(root.left, 0)) + Math.max(getMap.getOrDefault(root.right, 0), noGetMap.getOrDefault(root.right, 0)));
    }


    public int minCapability(int[] nums, int k) {
        //dp[k][i]表示  偷k次【0，i]符合条件的数量
        int dp[][] = new int[k][nums.length];
        int dp2[][] = new int[k][nums.length];
        for (int i = 0; i < nums.length; i++) {
            dp[0][i] = nums[i];
        }
        for (int i = 1; i < k; i++) {
            for (int j = i; j < nums.length; j++) {
                dp[i][j] = Math.max(dp2[i - 1][j] + nums[j], dp[i][j - 1]);
                dp2[i][j] = dp[i][j - 1];
            }
        }
        int min = Integer.MAX_VALUE;
        for (int i = 0; i < nums.length; i++) {
            min = Math.min(min, dp[k - 1][i]);
            min = Math.min(min, dp2[k - 1][i]);
        }
        return min;
    }


    public int[] fullBloomFlowers(int[][] flowers, int[] people) {
        int[] open = new int[flowers.length];
        int[] close = new int[flowers.length];
        for (int i = 0; i < flowers.length; i++) {
            open[i] = flowers[i][0];
            close[i] = flowers[i][1];
        }
        Arrays.sort(open);
        Arrays.sort(close);
        int result[] = new int[people.length];
        for (int i = 0; i < result.length; i++) {
            int live = bsearch(open, people[i]);
            int dead = bsearch(close, people[i] - 1);
            result[i] = live - dead;
        }
        return result;
    }

    //[0,arr.length） 里找到大于等于target的数
    private int bsearch(int[] arr, int target) {
        int left = 0;
        int right = arr.length;
        while (left < right) {
            int mid = left + (right - left) / 2;
            if (arr[mid] > target) {
                right = mid;
            } else {
                left = mid + 1;
            }
        }
        return left;
    }

    public String categorizeBox(int length, int width, int height, int mass) {
        long ti = length * 1L * width * height;
        int flag = 0;
        if (ti >= 1000000000 || length >= 10000 || width >= 10000 || height >= 10000 || mass >= 10000) {
            flag |= 1;
        }
        if (mass >= 100) {
            flag |= 2;
        }
        switch (flag) {
            case 0:
                return "Neither";
            case 1:
                return "Bulky";
            case 2:
                return "Heavy";
            default:
                return "Both";
        }
    }

    int tableSizeFor(int cap) {
        int n = cap - 1;
        n |= n >>> 1;
        n |= n >>> 2;
        n |= n >>> 4;
        n |= n >>> 8;
        n |= n >>> 16;
        return (n < 0) ? 1 : n + 1;
    }

    public int hIndex(int[] citations) {

        int left = 0;
        int right = citations.length;
        while (left < right) {
            int mid = (left + 1 + right) / 2;
            if (isH(citations, mid)) {
                left = mid;
            } else {
                right = mid - 1;
            }
        }
        return left;
    }

    private boolean isH(int[] array, int h) {
        int count = 0;
        for (int num : array) {
            if (num >= h) {
                count++;
            }
        }
        return count >= h;
    }


    public long minSum(int[] nums1, int[] nums2) {
        int left[] = getSimpleArray(nums1);
        int right[] = getSimpleArray(nums2);
        if (left[0] == 0 && right[0] > 0 && left[1] <= right[1] || right[0] == 0 && left[0] > 0 && left[1] >= right[1] || right[0] == left[0] && right[0] == 0 && right[1] != left[1]) {
            return -1;
        }
        if (right[0] > 0 && left[0] > 0) {
            return left[1] > right[1] ? left[1] + 1 : right[1] + 1;
        } else {
            return left[0] > 0 ? right[1] : left[1];
        }
    }

    private int[] getSimpleArray(int[] nums) {
        int arr[] = new int[2];
        for (int num : nums) {
            if (num == 0) {
                arr[0]++;
            } else {
                arr[1] += num;
            }
        }
        if (arr[0] > 1) {
            arr[1] += arr[0] - 1;
            arr[0] = 1;
        }
        return arr;
    }

    public int maxProduct(String[] words) {
        Map<Integer, Integer> wordMap = new HashMap<>();
        for (String word : words) {
            Integer id = word2Id(word);
            if (wordMap.containsKey(id)) {
                wordMap.put(id, Math.max(wordMap.get(id), word.length()));
            } else {
                wordMap.put(id, word.length());
            }
        }
        int max = 0;
        Set<Integer> set = wordMap.keySet();
        for (Integer id1 : set) {
            for (Integer id2 : set) {
                if ((id1 & id2) == 0) {
                    max = Math.max(max, wordMap.get(id1) * wordMap.get(id2));
                }
            }
        }
        return max;
    }

    private Integer word2Id(String word) {
        int base = 0;
        for (char c : word.toCharArray()) {
            base = base | 1 << (c - 'a' + 1);
        }
        return base;
    }

    public int findTheLongestBalancedSubstring(String s) {
        int zero = 0;
        int one = 0;
        int max = 0;
        char before = '1';
        for (int i = 0; i < s.length(); i++) {
            char current = s.charAt(i);
            if (current == '0') {
                if (before == '1') {
                    one = 0;
                    zero = 0;
                }
                zero++;
            }
            if (current == '1') {
                one++;
            }
            if (zero >= one) {
                max = Math.max(max, one * 2);
            }
            before = current;
        }

        return max;
    }

    public String entityParser(String text) {
        StringBuilder sb = new StringBuilder();
        StringBuilder tmp = new StringBuilder();
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (tmp.length() == 0) {
                if (c == '&') {
                    tmp.append(c);
                } else {
                    sb.append(c);
                }
            } else if (c == '&') {
                sb.append(tmp);
                tmp.setLength(0);
                tmp.append(c);
            } else {
                tmp.append(c);
                if (c == ';') {
                    switch (tmp.toString()) {
                        case "&quot;":
                            sb.append('"');
                            break;
                        case "&apos;":
                            sb.append('\'');
                            break;
                        case "&amp;":
                            sb.append('&');
                            break;
                        case "&gt;":
                            sb.append('>');
                            break;
                        case "&lt;":
                            sb.append('<');
                            break;
                        case "&frasl;":
                            sb.append('/');
                            break;
                        default:
                            sb.append(tmp);
                            break;
                    }
                    tmp.setLength(0);
                } else if (c != 'q' && c != 'u' && c != 'o' && c != 't' && c != 'a' && c != 'p' && c != 'g' && c != 'l' && c != 'f' && c != 's' && c != 'm' && c != 'r') {
                    sb.append(tmp);
                    tmp.setLength(0);
                }
            }
        }
        if (tmp.length() > 0) {
            sb.append(tmp);
        }
        return sb.toString();
    }


    public boolean closeStrings(String word1, String word2) {
        if (word1.length() != word2.length()) {
            return false;
        }
        int[] count = new int[26];
        int[] count2 = new int[26];
        for (int i = 0; i < word1.length(); i++) {
            count[word1.charAt(i) - 'a']++;
            count2[word2.charAt(i) - 'a']++;
        }
        for (int i = 0; i < count.length; i++) {
            if (count[i] > 0 && count2[i] == 0 || count[i] == 0 && count2[i] > 0) {
                return false;
            }
        }
        Arrays.sort(count);
        Arrays.sort(count2);
        for (int i = 0; i < count.length; i++) {
            if (count[i] != count2[i]) {
                return false;
            }
        }
        return true;
    }


    public int firstCompleteIndex(int[] arr, int[][] mat) {
        int x[] = new int[arr.length + 1];
        int y[] = new int[arr.length + 1];
        int maxX = mat.length;
        int maxY = mat[0].length;
        int xCount[] = new int[maxX];
        int yCount[] = new int[maxY];
        for (int i = 0; i < maxX; i++) {
            for (int j = 0; j < maxY; j++) {
                int val = mat[i][j];
                x[val] = i;
                y[val] = j;
            }
        }
        for (int i = 0; i < arr.length; i++) {
            if (++xCount[x[arr[i]]] == maxX) {
                return i;
            }
            if (++yCount[y[arr[i]]] == maxY) {
                return i;
            }
        }
        return 0;
    }


    public int minReorder(int n, int[][] connections) {
        int visited[] = new int[n];
        int count = 0;
        //start -> ends
        Map<Integer, List<Integer>> startMap = new HashMap<>();
        //end -> starts
        Map<Integer, List<Integer>> endMap = new HashMap<>();
        for (int[] connection : connections) {
            List<Integer> ends = startMap.getOrDefault(connection[0], new ArrayList<>());
            ends.add(connection[1]);
            startMap.putIfAbsent(connection[0], ends);

            List<Integer> starts = endMap.getOrDefault(connection[1], new ArrayList<>());
            starts.add(connection[0]);
            endMap.putIfAbsent(connection[1], starts);
        }
        Queue<Integer> queue = new LinkedList<>();
        queue.add(0);
        while (!queue.isEmpty()) {
            int node = queue.poll();
            List<Integer> ends = startMap.getOrDefault(node, new ArrayList<>());
            for (Integer end : ends) {
                if (visited[end] == 0) {
                    queue.add(end);
                    visited[end] = 1;
                    count++;
                }
            }
            List<Integer> starts = endMap.getOrDefault(node, new ArrayList<>());
            starts.stream().filter(start -> visited[start] == 0).forEach(start -> {
                queue.add(start);
                visited[start] = 1;
            });
        }
        return count;
    }


    public int[] secondGreaterElement(int[] nums) {
        int result[] = new int[nums.length];
        Arrays.fill(result, -1);
        PriorityQueue<Integer> foundBiggerIndexQueue = new PriorityQueue<>(Comparator.comparingInt(a -> nums[a]));
        Deque<Integer> stack = new ArrayDeque<>();
        stack.add(0);
        for (int i = 1; i < nums.length; i++) {
            int temp = nums[i];
            while (!foundBiggerIndexQueue.isEmpty() && nums[foundBiggerIndexQueue.peek()] < temp) {
                result[foundBiggerIndexQueue.poll()] = temp;
            }
            while (!stack.isEmpty() && nums[stack.peek()] < temp) {
                foundBiggerIndexQueue.add(stack.pop());
            }
            stack.push(i);
        }
        return result;
    }


    public boolean possibleToStamp(int[][] grid, int stampHeight, int stampWidth) {
        //距离右边第一个不可达的距离
        int dis2R[][] = new int[grid.length][grid[0].length];
        //距离左边
        int dis2L[][] = new int[grid.length][grid[0].length];
        //距离底部
        int dis2B[][] = new int[grid.length][grid[0].length];
        //距离高
        int dis2T[][] = new int[grid.length][grid[0].length];
        int last = -1;
        for (int i = 0; i < grid.length; i++) {
            for (int j = 0; j < grid[i].length; j++) {
                if (grid[i][j] == 1) {
                    last = j;
                }
                dis2R[i][j] = j - last;
            }
            last = -1;
        }
        last = grid[0].length;
        for (int i = 0; i < grid.length; i++) {
            for (int j = grid[i].length - 1; j >= 0; j--) {
                if (grid[i][j] == 1) {
                    last = j;
                }
                dis2L[i][j] = last - j;
            }
            last = grid[0].length;
        }
        last = -1;
        for (int i = 0; i < grid[0].length; i++) {
            for (int j = 0; j < grid.length; j++) {
                if (grid[j][i] == 1) {
                    last = j;
                }
                dis2B[j][i] = j - last;
            }
            last = -1;
        }

        last = grid.length;
        for (int i = 0; i < grid[0].length; i++) {
            for (int j = grid.length - 1; j >= 0; j--) {
                if (grid[j][i] == 1) {
                    last = j;
                }
                dis2T[j][i] = last - j;
            }
            last = grid.length;
        }

        for (int i = 0; i < grid.length; i++) {
            for (int j = 0; j < grid[i].length; j++) {
                if (grid[i][j] == 1) {
                    continue;
                }
                int width = dis2R[i][j] + dis2L[i][j] - 1;
                int height = dis2T[i][j] + dis2B[i][j] - 1;
                if (width < stampWidth || height < stampHeight) {
                    return false;
                }
            }
        }

        return true;
    }


    public long maximumSumOfHeights(List<Integer> maxHeights) {
        long max = 0L;
        long[] prefix = new long[maxHeights.size()];
        long[] suffix = new long[maxHeights.size()];
        Deque<Integer> stack1 = new ArrayDeque<>();
        Deque<Integer> stack2 = new ArrayDeque<>();

        for (int i = 0; i < maxHeights.size(); i++) {
            while (!stack1.isEmpty() && maxHeights.get(stack1.peek()) > maxHeights.get(i)) {
                stack1.pop();
            }
            if (stack1.isEmpty()) {
                prefix[i] = (long) maxHeights.get(i) * (i + 1);
            } else {
                prefix[i] = prefix[stack1.peek()] + (long) (i - stack1.peek()) * maxHeights.get(i);
            }
            stack1.push(i);
        }

        for (int i = maxHeights.size() - 1; i >= 0; i--) {
            while (!stack2.isEmpty() && maxHeights.get(stack2.peek()) > maxHeights.get(i)) {
                stack2.pop();
            }
            if (stack2.isEmpty()) {
                suffix[i] = (long) maxHeights.get(i) * (maxHeights.size() - i);
            } else {
                suffix[i] = suffix[stack2.peek()] + (long) (stack2.peek() - i) * maxHeights.get(i);
            }
            stack2.push(i);
            max = Math.max(max, prefix[i] + suffix[i] - maxHeights.get(i));
        }

        return max;
    }


    public int minimumMountainRemovals(int[] nums) {
        int pre[] = lengthOfLIS(nums);
        int suf[] = lengthOfLIS(getReverse(nums));
        int max = 0;
        for (int i = 0; i < pre.length; i++) {
            if (pre[i] > 1 && suf[pre.length - i - 1] > 1) {
                max = Math.max(max, (pre[i] + suf[pre.length - i - 1] - 1));
            }
        }
        return nums.length - max;
    }

    private int[] getReverse(int[] nums) {
        int[] result = new int[nums.length];
        for (int i = 0; i < nums.length; i++) {
            result[nums.length - i - 1] = nums[i];
        }
        return result;
    }

    public int[] lengthOfLIS(int[] nums) {
        List<Integer> dp = new ArrayList<>();
        //result[i] 长度为i的最长上升子序列的最小值
        int[] result = new int[nums.length];
        for (int i = 0; i < nums.length; i++) {
            int index = findSmaller(dp, nums[i]);
            if (index == dp.size()) {
                dp.add(nums[i]);
                result[i] = dp.size();
            } else {
                dp.set(index, nums[i]);
                result[i] = index + 1;
            }
        }
        return result;
    }

    public int findSmaller(List<Integer> dp, int num) {
        int left = 0;
        int right = dp.size();
        while (left < right) {
            int mid = (left + right) / 2;
            if (dp.get(mid) >= num) {
                right = mid;
            } else {
                left = mid + 1;
            }
        }
        return left;
    }


    public long minCost(int[] nums, int x) {
        int min = Integer.MAX_VALUE;
        int visited[] = new int[nums.length];
        int sumOp = 0;
        for (int num : nums) {
            min = Math.min(min, num);
        }
        int result = 0;
        for (int i = 0; i < nums.length; i++) {
            for (int j = 0; j < nums.length; j++) {
                if (visited[j] == 1) {
                    continue;
                }
                int nowIndex = (i + j) % nums.length;
                if (nums[nowIndex] == min || nums[nowIndex] < x || nums[j] - min <= x) {
                    result += nums[nowIndex];
                    visited[j] = 1;
                    sumOp++;
                }
            }
            if (sumOp == nums.length) {
                break;
            }
            result += x;
        }
        return result;
    }


    public int buyChoco(int[] prices, int money) {
        int min1 = Integer.MAX_VALUE;
        int min2 = Integer.MAX_VALUE;
        for (int price : prices) {
            if (price < min1) {
                min2 = min1;
                min1 = price;
            } else if (price < min2) {
                min2 = price;
            }
        }
        return min2 + min1 > money ? money : money - min1 - min2;
    }


    public int getMaxRepetitions(String s1, int n1, String s2, int n2) {

        Map<Integer, int[]> recall = new HashMap<>();
        int[] pre = null, after = null;
        int s1Count = 1, s2Count = 0, index = 0, total = 0;
        for (; s1Count <= n1; s1Count++) {
            for (int i = 0; i < s1.length(); i++) {
                if (s2.charAt(index) == s1.charAt(i)) {
                    index++;
                }
                if (index == s2.length()) {
                    index = 0;
                    s2Count++;
                }
            }
            if (s1Count == n1) {
                return s2Count / n2;
            }
            if (recall.get(index) != null) {
                pre = recall.get(index);
                after = new int[]{s1Count, s2Count};
                total += pre[1] + (n1 - pre[0]) / (after[0] - pre[0]) * (after[1] - pre[1]);
                break;
            } else {
                recall.put(index, new int[]{s1Count, s2Count});
            }
        }
        int count = (n1 - pre[0]) % (after[0] - pre[0]);
        for (int j = 0; j < count; j++) {
            for (int i = 0; i < s1.length(); i++) {
                if (s2.charAt(index) == s1.charAt(i)) {
                    index++;
                }
                if (index == s2.length()) {
                    index = 0;
                    total++;
                }
            }
        }

        return total / n2;
    }

    private String multi(String s, int n) {
        StringBuilder str1 = new StringBuilder();
        for (int i = 0; i < n; i++) {
            str1.append(s);
        }
        return str1.toString();
    }

    private int maxRep(String str1, String str2) {
        if (str1.length() == 0 || str2.length() == 0) {
            return 0;
        }
        int count = 0;
        int index = 0;
        for (int i = 0; i < str1.length(); i++) {
            if (str2.charAt(index) == str1.charAt(i)) {
                index++;
                if (index == str2.length()) {
                    index = 0;
                    count++;
                }
            }
        }
        return count;
    }


    public ListNode removeNodes(ListNode head) {
        Deque<Integer> stack = new ArrayDeque<>();
        ListNode pointer = head;
        while (pointer != null) {
            while (!stack.isEmpty() && stack.peek() < pointer.val) {
                stack.pop();
            }
            stack.push(pointer.val);
            pointer = pointer.next;
        }
        ListNode result = null;
        while (!stack.isEmpty()) {
            Integer val = stack.removeLast();
            if (result == null) {
                result = new ListNode(val, null);
                pointer = result;
            } else {
                pointer.next = new ListNode(val, null);
                pointer = pointer.next;
            }
        }
        return result;
    }


    public int maximumRows(int[][] matrix, int numSelect) {
        List<Integer> rows = new ArrayList<>();
        for (int i = 0; i < matrix.length; i++) {
            int num = 0;
            for (int j = 0; j < matrix[i].length; j++) {
                if (matrix[i][j] == 1) {
                    num |= (1 << j);
                }
            }
            rows.add(num);
        }
        List<Integer> maybeCaseList = new ArrayList<>();
        fillCase(maybeCaseList, 0, 0, matrix[0].length, numSelect);
        int max = 0;
        for (int situation : maybeCaseList) {
            int count = 0;
            for (int row : rows) {
                if ((row & situation) == row) {
                    count++;
                }
            }
            max = Math.max(max, count);
        }
        return max;
    }

    private void fillCase(List<Integer> mayCaseList, int base, int start, int end, int need) {
        if (need == 0) {
            mayCaseList.add(base);
            return;
        }
        for (int i = start; i < end; i++) {
            base |= (1 << i);
            fillCase(mayCaseList, base, i + 1, end, need - 1);
            base ^= (1 << i);
        }
    }


    public int numberOfBoomerangs1(int[][] points) {
        Map<Integer, Map<Integer, Integer>> theSameDistanceMap = new HashMap<>();
        int base = 10000;
        for (int[] point : points) {
            int key = (point[0] + base) * base + point[1];
            Map<Integer, Integer> theSameDistanceInnerMap = theSameDistanceMap.getOrDefault(key, new HashMap<>());
            for (int[] point2 : points) {
                if (point == point2) {
                    continue;
                }
                int dis = dis(point, point2);
                theSameDistanceInnerMap.put(dis, theSameDistanceInnerMap.getOrDefault(dis, 0) + 1);
            }
            theSameDistanceMap.putIfAbsent(key, theSameDistanceInnerMap);
        }
        int count = 0;
        for (Map<Integer, Integer> countMap : theSameDistanceMap.values()) {
            if (countMap != null) {
                for (int value : countMap.values()) {
                    count += value * (value - 1);
                }
            }
        }

        return count;
    }


    public int numberOfBoomerangs(int[][] points) {
        Map<Integer, Integer> theSameDistanceMap = new HashMap<>();
        int count = 0;
        for (int[] point : points) {
            for (int[] point2 : points) {
                if (point == point2) {
                    continue;
                }
                int dis = dis(point, point2);
                theSameDistanceMap.put(dis, theSameDistanceMap.getOrDefault(dis, 0) + 1);
            }
            for (int value : theSameDistanceMap.values()) {
                count += value * (value - 1);
            }
            theSameDistanceMap.clear();
        }
        return count;
    }

    private int dis(int[] point1, int[] point2) {
        return (point1[0] - point2[0]) * (point1[0] - point2[0]) + (point1[1] - point2[1]) * (point1[1] - point2[1]);
    }


    public String baseNeg2(int n) {
        if (n == 0) {
            return "0";
        }
        int tmp = findBase2(n);
        while (tmp != n) {
            n = tmp - n;

        }
        StringBuilder result = new StringBuilder();
        while (tmp > 0) {
            result.insert(0, tmp % 2);
            tmp /= 2;
        }
        return result.toString();
    }

    private int findBase2(int x) {
        int n = x - 1;
        n |= n >>> 1;
        n |= n >>> 2;
        n |= n >>> 4;
        n |= n >>> 8;
        n |= n >>> 16;
        return (n < 0) ? 1 : n + 1;
    }

    public int minimumAddedInteger(int[] nums1, int[] nums2) {
        Arrays.sort(nums1);
        Arrays.sort(nums2);
        int min = 99999;
        for (int i = 0; i < nums1.length; i++) {
            for (int j = i + 1; j < nums1.length; j++) {
                int result = judge(nums1, nums2, i, j);
                if (result != -9999) {
                    return result;
                }
            }
        }
        return min;
    }

    private int judge(int[] nums1, int[] nums2, int i, int j) {
        int grep = -9999;
        int index = 0;
        for (int x = 0; x < nums1.length; x++) {
            if (x == i || x == j) {
                continue;
            }
            if (grep == -9999) {
                grep = nums2[index] - nums1[x];
            } else if (grep != nums2[index] - nums1[x]) {
                return -9999;
            }
            index++;
        }
        return grep;
    }

    public int[] mostCompetitive(int[] nums, int k) {
        int result[] = new int[k];
        int index = 0;
        Queue<Integer> queue = new PriorityQueue<>((a, b) -> {
            if (nums[a] != nums[b]) {
                return nums[a] - nums[b];
            }
            return a - b;
        });
        int tail = nums.length - k + 1;
        for (int i = 0; i < tail; i++) {
            queue.add(i);
        }
        int lastPosition = 0;
        while (index < result.length) {
            int minIndex = queue.poll();
            result[index++] = nums[minIndex];
            while (lastPosition <= minIndex) {
                queue.remove(lastPosition);
                lastPosition++;
            }
            if (tail < nums.length) {
                queue.add(tail++);
            }
        }
        return result;
    }

    public long minEnd(int n, int x) {
        long result = x;
        int last = n - 1;
        long index = 1;
        while (last > 0) {
            long value = last & 1;
            while ((x & index) != 0) {
                index = index << 1;
            }
            if (value != 0) {
                result = result | index;
            }
            index = index << 1;
            last >>= 1;
        }
        return result;
    }


    public long minimumCost(int m, int n, int[] horizontalCut, int[] verticalCut) {
        long res = 0;
        Arrays.sort(horizontalCut);
        Arrays.sort(verticalCut);
        int horizontalCount = horizontalCut.length - 1;
        int verticalCount = verticalCut.length - 1;
        long hCount = 1;
        long vCount = 1;
        while (horizontalCount >= 0 || verticalCount >= 0) {
            if (verticalCount < 0 || horizontalCount >= 0 && horizontalCut[horizontalCount] > verticalCut[verticalCount]) {
                hCount++;
                res += horizontalCut[horizontalCount] * vCount;
                horizontalCount--;
            } else {
                vCount++;
                res += verticalCut[verticalCount] * hCount;
                verticalCount--;
            }
        }
        return res;
    }

    public int maxArea(int[] height) {
        long max = 0;
        int left = 0;
        int right = height.length - 1;
        while (left != right) {
            max = Math.max(max, (long) (right - left) * Math.min(height[left], height[right]));
            if (height[left] < height[right]) {
                left++;
            } else {
                right--;
            }
        }
        return (int) max;
    }

    public boolean checkPowersOfThree(int n) {
        List<Integer> list = new ArrayList<>();
        int base = 1;
        while (base < n) {
            list.add(base);
            base *= 3;
        }
        for (int i = list.size() - 1; i >= 0; i--) {
            int base2 = list.get(i);
            int multi = n / base2;
            if (multi > 1) {
                return false;
            } else if (multi == 1) {
                n -= base2;
            }
        }

        return n < 2;
    }

    public boolean isPowerOfThree(int n) {

        while (n != 1) {
            if (n % 3 != 0 || n <= 0) {
                return false;
            }
            n /= 3;
        }
        return true;
    }

    public int numberOfWays(int n, int x) {
        int[] baseList = new int[n + 1];
        int b_size = 0;
        int mod = (int) 1e9 + 7;
        for (int i = 1; ; i++) {
            int value = (int) Math.pow(i, x);
            if (value <= n) {
                baseList[b_size++] = value;
            } else {
                break;
            }
        }
        long[] dp = new long[n + 1];
        dp[0] = 1;
        for (int i = 0; i < b_size; i++) {
            int value = baseList[i];
            for (int j = n; j >= value; j--) {
                if (dp[j - value] > 0) {
                    dp[j] = (dp[j] + dp[j - value]) % mod;
                }
            }
        }

        return (int) dp[n];
    }


    public int[] productQueries(int n, int[][] queries) {

        List<Long> list = new ArrayList<>();
        int base = 1;
        while (base <= n) {
            list.add((long) base);
            base *= 2;
        }
        List<Long> baseArrayList = new ArrayList<>();
        int temp = n;
        for (int i = list.size() - 1; i >= 0; i--) {
            long value = list.get(i);
            if (temp / value > 0) {
                int times = (int) (temp / value);
                while (times-- > 0) {
                    baseArrayList.add(value);
                }
            }
            temp %= value;
        }
        baseArrayList.sort(Comparator.comparingLong(l -> l));
        int result [] = new int[queries.length];
        for (int i = 0; i < queries.length; i++) {
            result[i] = 1;
            for(int j = queries[i][0]; j <= queries[i][1]; j++) {
                result[i] = (int) (((result[i] % (1e9 + 7))  * (baseArrayList.get(j)% (1e9 + 7))) % (1e9 + 7));

            }
        }
        return result;
    }

    public JSONObject formatAdditionalData(Map<String, Object> extraData) {
        JSONObject additionalData = new JSONObject();
        Map<String, JSONObject> tempMap = new HashMap<>();
        extraData.forEach((key, value) -> {
            if (!key.startsWith("MA.")) {
                return;
            }
            Object realValue = value;
            if (value instanceof String && ((String) value).startsWith("{")) {
                realValue = JSONObject.toJSONString(value);
            }
            String realKey = key.replace("MA.", "");
            String[] keys = realKey.split("\\.");
            String currentKey = "";
            JSONObject last = additionalData;
            for (int i = 0; i < keys.length; i++) {
                currentKey += keys[i];
                if (i < keys.length - 1) {
                    if (tempMap.containsKey(currentKey)) {
                        last = tempMap.get(currentKey);
                    } else {
                        JSONObject temp = new JSONObject();
                        last.put(keys[i], temp);
                        last = temp;
                        tempMap.put(currentKey, last);
                    }
                    currentKey += ".";
                } else {
                    last.put(keys[i], realValue);
                }
                if(i == 0){
                    additionalData.put(keys[i], last);
                }
            }
        });
        return additionalData;
    }
}

